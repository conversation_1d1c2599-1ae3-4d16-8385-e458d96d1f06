import pandas as pd
import requests
import os

def batch_call_api(input_excel):
    # 读取输入Excel文件的第一列
    df = pd.read_excel(input_excel, usecols=[0], header=None, names=["content"])
    url = "http://111.231.11.187:9105/nlp_service/bond_robot_v2/content/analysis"

    # 初始化结果列表，用于存储每个接口调用的返回数据
    results = []

    # 遍历每个输入的content，调用接口
    for content in df["content"]:
        payload = {"content": content}
        try:
            # 调用接口
            response = requests.post(url, json=payload)
            response_data = response.json()

            # 提取intent的label字段
            if response_data["code"] == 1 and "data" in response_data:
                intent_data = response_data["data"].get("intent", [])
                label = intent_data[0].get("label", "无标签") if intent_data else "无标签"

                # 提取订单信息（order_info）
                structure_data = response_data["data"].get("structure", [])

                # 提取待定要素
                auxiliary_info_data = response_data["data"].get("auxiliary_info", {})

                order_info_list = []
                for structure_item in structure_data:
                    order_info = structure_item.get("order_info", {})
                    # 仅当字段不为空时将其添加至订单信息字符串
                    direction = f"方向: {order_info['direction']}" if order_info.get("direction") else ""
                    bond_name = f"债券名称: {order_info['bond_name']}" if order_info.get("bond_name") else ""
                    bond_code = f"债券代码: {order_info['bond_code']}" if order_info.get("bond_code") else ""
                    bond_trade_price = f"交易价格: {order_info['bond_trade_price']}" if order_info.get("bond_trade_price") else ""
                    bond_trade_price_margin = f"价差: {order_info['bond_trade_price_margin']}" if order_info.get("bond_trade_price_margin") else ""
                    bond_trade_price_yield_type = f"价格类型: {order_info['bond_trade_price_yield_type']}" if order_info.get("bond_trade_price_yield_type") else ""
                    bond_trade_volume = f"交易数量: {order_info['bond_trade_volume']}" if order_info.get("bond_trade_volume") else ""


                    numeric_info_list = auxiliary_info_data.get("numeric",[])

                    numeric_info_str = ", ".join(filter(None, numeric_info_list))
                    numeric_info = f"待定要素：{numeric_info_str}" if numeric_info_str else ""
                    # 将非空的字段拼接成一条订单信息
                    order_info_str = ", ".join(filter(None, [direction, bond_name, bond_code, bond_trade_price,bond_trade_price_margin,bond_trade_price_yield_type, bond_trade_volume, numeric_info]))
                    if order_info_str:
                        order_info_list.append(order_info_str)

                # 多笔订单信息使用换行符分隔
                order_info_combined = "\n".join(order_info_list) if order_info_list else "无订单信息"
            else:
                label = "无标签"
                order_info_combined = "无订单信息"
                
            # 将结果添加到列表
            results.append({"输入内容": content, "意图标签": label, "订单信息": order_info_combined})

        except Exception as e:
            print(f"调用接口时出错：{e}")
            results.append({"输入内容": content, "意图标签": "调用失败", "订单信息": "调用失败"})

    # 转换为DataFrame并导出到Excel文件
    output_df = pd.DataFrame(results)
    input_info = input_excel.split("/")[-1].replace(".xlsx","")
    output_file = os.path.join(os.path.dirname(input_excel), f"{input_info}_output_results.xlsx")
    output_df.to_excel(output_file, index=False)

    print(f"已将结果保存至: {output_file}")

# 输入Excel文件的路径
input_excel = r"/root/project/上下文解析跑批/20241115.xlsx"
batch_call_api(input_excel)
