# pip install arxiv

import arxiv

# 定义查询关键词
query = "agent"

# 执行搜索
search = arxiv.Search(
    query=query,
    max_results=5,  # 限制返回论文数量
    sort_by=arxiv.SortCriterion.SubmittedDate  # 按提交时间排序
)

# 打印论文信息
for result in search.results():
    print(f"标题: {result.title}")
    print(f"作者: {', '.join([author.name for author in result.authors])}")
    print(f"摘要: {result.summary}")
    print(f"发布时间: {result.published}")
    print(f"PDF链接: {result.pdf_url}\n")