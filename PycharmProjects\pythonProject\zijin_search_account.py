import json
import openpyxl
import os

def extract_account_names_to_excel(input_file_path):
    """
    从 .jsonl 文件中提取账户名并将其写入 Excel 文件的第一列。

    Args:
        input_file_path (str): 输入 .jsonl 文件的路径。
    """
    account_names = []
    try:
        with open(input_file_path, 'r', encoding='utf-8') as f:
            for line in f:
                try:
                    data = json.loads(line.strip())
                    if "entities" in data and isinstance(data["entities"], list):
                        for entity in data["entities"]:
                            if len(entity) >= 4 and entity[3] == "账户名":
                                account_names.append(entity[2])
                except json.JSONDecodeError:
                    print(f"警告: 无法解析行: {line.strip()}")
    except FileNotFoundError:
        print(f"错误: 文件未找到: {input_file_path}")
        return

    if account_names:
        workbook = openpyxl.Workbook()
        sheet = workbook.active
        for name in account_names:
            sheet.append([name])

        output_file_path = os.path.splitext(input_file_path)[0] + ".xlsx"
        try:
            workbook.save(output_file_path)
            print(f"成功将账户名提取到 Excel 文件: {output_file_path}")
        except Exception as e:
            print(f"保存 Excel 文件时发生错误: {e}")
    else:
        print("文件中没有找到账户名。")

if __name__ == "__main__":
    input_file = input("请输入 .jsonl 文件的路径: ")
    extract_account_names_to_excel(input_file)