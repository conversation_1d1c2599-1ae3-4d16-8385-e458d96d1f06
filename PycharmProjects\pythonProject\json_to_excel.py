# -*- coding: utf-8 -*-
"""
本脚本的主要功能是读取一个包含JSON字符串的TXT文件，
从中提取特定结构（`data.list`）下的所有不重复的`content`字段的值，
过滤掉超过80个字符的content文本，
然后将符合条件的值写入到一个新的Excel文件中。
"""

# 导入所需的库
import json  # 用于解析JSON数据
import pandas as pd  # 用于创建数据框（DataFrame）并将其导出为Excel文件
import os    # 用于处理文件路径和文件名
import requests  # 用于发送HTTP请求

def extract_content_to_excel(input_txt_path):
    """
    从包含JSON的TXT文件中读取内容，提取`data.list`数组中所有不重复的'content'值，
    过滤掉超过80个字符的content文本，并将符合条件的值写入到Excel文件中。

    Args:
        input_txt_path (str): 输入的TXT文件的路径。
    """
    try:
        # --- 1. 读取TXT文件内容 ---
        # 使用 'with' 语句确保文件能被正确打开和关闭
        # 'r' 表示读取模式, 'encoding='utf-8'' 确保能正确处理中文字符
        with open(input_txt_path, 'r', encoding='utf-8') as f:
            json_string = f.read()

        # --- 2. 解析JSON数据 ---
        # 将从文件中读取的字符串转换为Python的字典或列表对象
        data = json.loads(json_string)

        # --- 3. 验证JSON结构并提取数据 ---
        # 检查JSON数据是否符合预期的格式，即`code`字段为0，且包含`data`和`list`键
        if data.get("code") == 0 and "data" in data and "list" in data["data"]:
            content_list = data["data"]["list"]
            
            # --- 4. 提取并去重'content'字段的值 ---
            # 使用集合（set）来自动去除重复的'content'值，效率更高
            # 同时过滤掉超过80个字符的content文本
            unique_contents = set()
            for item in content_list:
                if "content" in item:
                    content_text = item["content"]
                    # 只添加字符数不超过80的content
                    if len(content_text) <= 50:
                        unique_contents.add(content_text)
            
            # 如果没有找到任何'content'值，则打印提示信息并退出函数
            if not unique_contents:
                print("在JSON数据中未找到任何 'content' 值。")
                return

            # 将集合转换为列表，以便后续创建DataFrame
            output_data = list(unique_contents)

            # --- 5. 创建Pandas DataFrame ---
            # 将提取出的数据转换为DataFrame，并指定列名为 "Content"
            df = pd.DataFrame(output_data, columns=["Content"])

            # --- 6. 构建输出的Excel文件名 ---
            # 获取输入文件的基本名称（例如 "data.txt"）
            base_name = os.path.basename(input_txt_path)
            # 分离文件名和扩展名，得到不带扩展名的文件名
            file_name_without_ext = os.path.splitext(base_name)[0]
            # 构建新的Excel文件名，格式为 "原文件名_output.xlsx"
            output_excel_path = f"{file_name_without_ext}_output.xlsx"
            
            # 获取输入文件的目录，以确保输出文件保存在同一位置
            output_dir = os.path.dirname(input_txt_path)
            # 拼接得到完整的输出文件路径
            full_output_path = os.path.join(output_dir, output_excel_path)
            
            # 如果输入路径只是一个文件名（表示在当前工作目录），则输出路径也应在当前目录
            if not output_dir:
                 full_output_path = output_excel_path

            # --- 7. 将DataFrame写入Excel文件 ---
            # `index=False` 表示在写入Excel时不要包含DataFrame的索引列
            df.to_excel(full_output_path, index=False)

            print(f"成功提取不重复的content内容到: {full_output_path}")

        else:
            # 如果JSON结构不符合预期，打印错误信息
            print("错误: JSON结构不符合预期或 'code' 字段不为 0。")

    # --- 8. 异常处理 ---
    except FileNotFoundError:
        # 如果找不到输入文件
        print(f"错误: 在路径 {input_txt_path} 未找到输入文件。")
    except json.JSONDecodeError:
        # 如果文件内容不是有效的JSON格式
        print(f"错误: 无法解析文件 {input_txt_path} 中的JSON。请确保文件内容是有效的JSON格式。")
    except Exception as e:
        # 捕获其他所有预料之外的异常
        print(f"发生未知错误: {e}")

# --- 脚本执行入口 ---
# `if __name__ == "__main__"`确保以下代码只在直接运行此脚本时执行，
# 而在其他脚本中导入此文件时不会执行。
if __name__ == "__main__":
    # 提示用户输入包含JSON数据的TXT文件路径
    txt_file_path = input("请输入包含 JSON 数据的 TXT 文件路径: ")
    # 调用主函数，执行提取和转换操作
    extract_content_to_excel(txt_file_path)