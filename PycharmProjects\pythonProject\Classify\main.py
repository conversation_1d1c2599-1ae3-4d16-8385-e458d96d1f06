import pandas as pd
import requests
import concurrent.futures
import time
import os
from tqdm import tqdm


def process_excel_with_api():
    # 文件路径处理
    # 新增用户输入处理模块
    while True:
        input_path = input("请输入Excel文件完整路径：").strip('"').strip()
        if os.path.exists(input_path) and input_path.lower().endswith('.xlsx'):
            break
        print("错误：路径无效或非xlsx文件，请重新输入")
    output_path = os.path.splitext(input_path)[0] + '-已分类.xlsx'

    # 读取Excel文件[1,2,5](@ref)
    df = pd.read_excel(input_path, engine='openpyxl')
    contents = df.iloc[:, 0].tolist()
    results = [None] * len(contents)

    # 定义API请求函数[7,8](@ref)
    def api_request(index, content):
        payload = {
            "content": content,
            "message_id": "MSG-ID-101",
            "sender_id": "SENDER-ID-101",
            "receiver_id": "RECEIVER-ID-102",
            "session_id": "SESSION-ID-101",
            "category": ["一级", "二级"],
            "sender_info": "",
            "receiver_info": "",
            "timestamp": 1665282770
        }
        try:
            response = requests.post(
                "http://**************:9066/nlp_service/classifier/universal",
                json=payload,
                timeout=10
            )
            if response.ok:
                data = response.json()
                if data.get('code') == 0:
                    classify = data.get('data', {}).get('classify', [])
                    return index, ', '.join(classify)
                return index, f"Error: {data.get('msg', 'Unknown error')}"
            return index, f"HTTP Error: {response.status_code}"
        except Exception as e:
            return index, f"Exception: {str(e)}"

    # 并发处理[6,7,8](@ref)
    with concurrent.futures.ThreadPoolExecutor(max_workers=30) as executor:
        futures = []
        start_time = time.time()

        # 提交任务
        for idx, content in enumerate(contents):
            futures.append(executor.submit(api_request, idx, content))

        # 进度监控[8](@ref)
        with tqdm(total=len(futures), desc="处理进度") as pbar:
            for future in concurrent.futures.as_completed(futures):
                idx, result = future.result()
                results[idx] = result

                # 计算剩余时间
                elapsed = time.time() - start_time
                processed = pbar.n + 1
                remaining = len(futures) - processed
                eta = (elapsed / processed) * remaining if processed else 0

                pbar.set_postfix({
                    "已处理": f"{processed}/{len(futures)}",
                    "剩余时间": f"{eta:.1f}s"
                })
                pbar.update(1)

    # 写入结果并保存[2,5](@ref)
    df.insert(1, '分类结果', results)  # 在第二列插入结果
    df.to_excel(output_path, index=False, engine='openpyxl')
    print(f"\n处理完成！文件已保存至：{output_path}")


if __name__ == "__main__":
    process_excel_with_api()