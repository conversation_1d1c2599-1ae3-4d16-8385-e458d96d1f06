import os
import sys


def batch_rename_excel_to_csv(folder_path):
    """
    批量将指定文件夹及其子文件夹中的Excel文件(.xls和.xlsx)重命名为CSV文件(.csv)

    参数:
        folder_path (str): 要处理的文件夹路径
    """
    # 检查文件夹是否存在
    if not os.path.isdir(folder_path):
        print(f"错误: 文件夹 '{folder_path}' 不存在!")
        return

    # 计数器
    renamed_count = 0
    error_count = 0

    # 遍历文件夹及其子文件夹
    for root, dirs, files in os.walk(folder_path):
        for file in files:
            # 检查文件是否为Excel文件
            if file.lower().endswith(('.xls', '.xlsx')):
                # 获取原文件的完整路径
                original_path = os.path.join(root, file)

                # 获取文件名和扩展名
                file_name, file_ext = os.path.splitext(file)

                # 创建新的文件名(使用.csv扩展名)
                new_file = file_name + '.csv'
                new_path = os.path.join(root, new_file)

                try:
                    # 重命名文件
                    os.rename(original_path, new_path)
                    print(f"已重命名: {original_path} -> {new_path}")
                    renamed_count += 1
                except Exception as e:
                    print(f"重命名文件 '{original_path}' 时出错: {str(e)}")
                    error_count += 1

    # 打印统计信息
    print("\n处理完成!")
    print(f"成功重命名文件数: {renamed_count}")
    print(f"处理失败文件数: {error_count}")


if __name__ == "__main__":
    # 如果通过命令行提供了文件夹路径，则使用该路径
    if len(sys.argv) > 1:
        folder_path = sys.argv[1]
    else:
        # 否则使用默认路径或请求用户输入
        folder_path = input("请输入要处理的文件夹路径: ")

    # 执行批量重命名
    batch_rename_excel_to_csv(folder_path)
