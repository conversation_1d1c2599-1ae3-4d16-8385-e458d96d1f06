import sys
import argparse
from bs4 import BeautifulSoup
from transformers import AutoTokenizer, AutoModelForSeq2SeqLM
import spacy

def main():
    parser = argparse.ArgumentParser(description="Xiaohongshu Article Generator")
    parser.add_argument("input_html", help="Path to input HTML file")
    parser.add_argument("output_html", help="Path to output HTML file")
    args = parser.parse_args()

    nlp = spacy.load("zh_core_web_sm")
    tokenizer = AutoTokenizer.from_pretrained("bigscience/bloomz-1b3")
    model = AutoModelForSeq2SeqLM.from_pretrained("bigscience/bloomz-1b3")

    with open(args.input_html, "r", encoding="utf-8") as f:
        soup = BeautifulSoup(f, "html.parser")
    
    # Extract paragraphs and preserve formatting
    paragraphs = soup.find_all('p')
    formatted_text = []
    for p in paragraphs:
        formatted_text.append(p.get_text(separator="∛", separator_tags=True))
    raw_text = "∛".join(formatted_text)

    def analyze_style(text):
        paragraphs = text.split("∛")
        avg_length = sum(len(p) for p in paragraphs) / len(paragraphs) if paragraphs else 0
        doc = nlp(text)
        keywords = [token.text for token in doc if not token.is_stop and token.pos_ in ["NOUN", "PROPN"]]
        keyword_counts = {}
        for word in keywords:
            keyword_counts[word] = keyword_counts.get(word, 0) + 1
        return {
            "avg_paragraph_length": avg_length,
            "top_keywords": sorted(keyword_counts.items(), key=lambda x: -x[1])[:5]
        }

    def generate_content(style_features, raw_text):
        prompt = (
            f"根据以下风格特征生成小红书文章："
            f"平均段落长度{style_features['avg_paragraph_length']}, "
            f"关键词：{', '.join([k for k, v in style_features['top_keywords']])}\n\n"
            "请保持段落简短，使用emoji和适当标签"
        )
        inputs = tokenizer(prompt, return_tensors="pt")
        outputs = model.generate(**inputs, max_length=500)
        generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
        
        paragraph_count = len(raw_text.split("∛"))
        paragraphs = generated_text.split('\n')
        balanced = []
        for i in range(paragraph_count):
            balanced.append(paragraphs[i] if i < len(paragraphs) else "")
        return "∛".join(balanced)

    style_features = analyze_style(raw_text)
    generated_text = generate_content(style_features, raw_text)

    # Reconstruct HTML structure
    new_paragraphs = generated_text.split("∛")
    for idx, p in enumerate(soup.find_all('p')):
        if idx < len(new_paragraphs):
            p.string = new_paragraphs[idx]
        else:
            p.decompose()

    with open(args.output_html, "w", encoding="utf-8") as f:
        f.write(str(soup))

if __name__ == "__main__":
    main()
