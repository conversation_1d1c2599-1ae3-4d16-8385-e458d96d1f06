
import json
import pandas as pd
import os
import argparse
import sys
from pathlib import Path

def jsonl_to_excel(jsonl_file_path, excel_file_path):
    """
    读取.jsonl文件，提取'text'、'truth'和'pred'字段，
    并将它们写入Excel文件。

    Args:
        jsonl_file_path (str): 输入.jsonl文件的路径
        excel_file_path (str): 输出.xlsx文件的路径

    Returns:
        bool: 转换是否成功
    """
    # 检查输入文件是否存在
    if not os.path.exists(jsonl_file_path):
        print(f"错误：输入文件 '{jsonl_file_path}' 不存在！")
        return False

    data = []
    line_count = 0
    error_count = 0

    try:
        with open(jsonl_file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:  # 跳过空行
                    continue

                line_count += 1
                try:
                    # 解析每行的JSON对象
                    json_obj = json.loads(line)

                    # 提取必需的字段
                    text = json_obj.get('text', '')
                    truth = json_obj.get('truth', '')
                    pred = json_obj.get('pred', '')

                    # 检查是否缺少必需字段
                    if not all([key in json_obj for key in ['text', 'truth', 'pred']]):
                        print(f"警告：第{line_num}行缺少必需字段，跳过该行")
                        error_count += 1
                        continue

                    # 添加到数据列表
                    data.append([text, truth, pred])

                except json.JSONDecodeError as e:
                    print(f"警告：第{line_num}行JSON格式错误，跳过该行: {e}")
                    error_count += 1
                    continue

    except Exception as e:
        print(f"错误：读取文件时发生错误: {e}")
        return False

    if not data:
        print("错误：没有找到有效的数据行！")
        return False

    try:
        # 创建pandas DataFrame
        df = pd.DataFrame(data, columns=['text', 'truth', 'pred'])

        # 确保输出目录存在
        output_dir = os.path.dirname(excel_file_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)

        # 写入Excel文件
        df.to_excel(excel_file_path, index=False)

        print(f"✅ 转换成功！")
        print(f"   输入文件: {jsonl_file_path}")
        print(f"   输出文件: {excel_file_path}")
        print(f"   处理行数: {line_count}")
        print(f"   有效数据: {len(data)}行")
        if error_count > 0:
            print(f"   错误行数: {error_count}")

        return True

    except Exception as e:
        print(f"错误：写入Excel文件时发生错误: {e}")
        return False

def main():
    """主函数，处理命令行参数并执行转换"""
    parser = argparse.ArgumentParser(
        description='将JSONL文件转换为Excel文件',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog='''
使用示例:
  python yitufenlei_compare.py input.jsonl
  python yitufenlei_compare.py input.jsonl -o output.xlsx
  python yitufenlei_compare.py /path/to/data.jsonl -o /path/to/result.xlsx

JSON格式示例:
  {"text": "这债的区间是多少呀", "truth": "问量", "pred": "问价"}
  {"text": "暂时放的23哇", "truth": "报价", "pred": "报量"}
        '''
    )

    parser.add_argument(
        'input_file',
        help='输入的JSONL文件路径'
    )

    parser.add_argument(
        '-o', '--output',
        help='输出的Excel文件路径（可选，默认为输入文件名_output.xlsx）'
    )

    args = parser.parse_args()

    # 处理输入文件路径
    input_path = args.input_file

    # 处理输出文件路径
    if args.output:
        output_path = args.output
    else:
        # 默认输出文件名：输入文件名_output.xlsx
        input_file = Path(input_path)
        output_path = input_file.parent / f"{input_file.stem}_output.xlsx"

    # 执行转换
    success = jsonl_to_excel(input_path, str(output_path))

    if not success:
        sys.exit(1)

if __name__ == '__main__':
    main()
