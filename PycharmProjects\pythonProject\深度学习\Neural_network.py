import  numpy as np

# 输入层到第一层
X = np.array([1.0, 0.5])

W1 = np.array([[0.1,0.3,0.5],[0.2,0.4,0.6]])

B1 = np.array([0.1,0.2,0.3])

print(W1.shape)

print(X.shape)

A1 = np.dot(X, W1) + B1

print(A1)

def sigmoid(x):
    return 1/(1+np.exp(-x))

Z1 = sigmoid(A1)
print(Z1)

# 第一层到第二层
W2 = np.array([[0.1,0.4],[0.2,0.5],[0.3,0.6]])
B2 = np.array([0.1,0.2])

A2 = np.dot(Z1, W2)+B2
print(A2)

Z2 = sigmoid(A2)

print(Z2)

# 第二层到输出层
def identity_function(x):
    return x

W3 = np.array([[0.1,0.3],[0.2,0.4]])
B3 = np.array([0.1,0.2])

A3 = np.dot(Z2, W3) + B3

Y = identity_function(A3)

print(Y)

# softmax输出函数，通常用于分类问题
def softmax(a):
    c = np.max(a)
    exp_a = np.exp(a-c)  # 防止计算溢出
    y = exp_a / np.sum(exp_a)
    return y
