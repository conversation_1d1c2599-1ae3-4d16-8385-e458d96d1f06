import os
import pandas as pd
import time
import threading
from concurrent.futures import Thread<PERSON>oolExecutor
from tqdm import tqdm
import shutil
from datetime import datetime, timedelta


def process_excel(excel_file, input_folder):
    """处理单个 Excel 文件"""
    try:
        file_name = os.path.basename(excel_file)
        file_name_without_ext = os.path.splitext(file_name)[0]

        # 创建输出文件夹
        output_folder = os.path.join(input_folder, f"{file_name_without_ext}_output")
        os.makedirs(output_folder, exist_ok=True)

        # 读取 Excel 文件
        df = pd.read_excel(excel_file)

        # 按照"会话方A"-"会话方B"分组
        groups = df.groupby(['会话方A', '会话方B'])

        # 处理每个分组
        for idx, ((party_a, party_b), group_df) in enumerate(groups, 1):
            # 创建新的 DataFrame 用于输出
            output_df = pd.DataFrame(columns=['发送方', '会话ID', '消息ID', '消息原文', '消息时间'])

            # 填充数据
            output_df['消息ID'] = group_df['session_id']
            output_df['消息原文'] = group_df['消息内容']
            output_df['消息时间'] = group_df['消息消息时间']

            # 根据消息方向确定发送方
            output_df['发送方'] = group_df.apply(
                lambda row: row['会话方A'] if row['消息方向'] == '发送' else row['会话方B'],
                axis=1
            )

            # 生成会话ID（从1开始自增）
            output_df['会话ID'] = range(1, len(output_df) + 1)

            # 保存到新的 Excel 文件
            output_file = os.path.join(output_folder, f"{file_name_without_ext}-{idx}.xlsx")
            output_df.to_excel(output_file, index=False)

        return True
    except Exception as e:
        print(f"处理文件 {excel_file} 时出错: {str(e)}")
        return False


def process_folder(input_folder, max_workers=10):
    """处理文件夹中的所有 Excel 文件"""
    # 获取所有 Excel 文件
    excel_files = []
    for file in os.listdir(input_folder):
        if file.endswith(('.xlsx', '.xls')) and not file.endswith('_output.xlsx'):
            excel_files.append(os.path.join(input_folder, file))

    total_files = len(excel_files)
    if total_files == 0:
        print("没有找到 Excel 文件！")
        return

    print(f"找到 {total_files} 个 Excel 文件需要处理")

    # 创建进度条
    progress_bar = tqdm(total=total_files, desc="处理进度")

    # 记录开始时间
    start_time = time.time()
    processed_count = 0

    # 线程锁，用于更新进度
    lock = threading.Lock()

    def update_progress(result):
        nonlocal processed_count
        with lock:
            processed_count += 1
            progress_bar.update(1)

            # 计算剩余时间
            elapsed_time = time.time() - start_time
            if processed_count > 0:
                avg_time_per_file = elapsed_time / processed_count
                remaining_files = total_files - processed_count
                estimated_time_remaining = avg_time_per_file * remaining_files

                # 格式化剩余时间
                remaining_time = str(timedelta(seconds=int(estimated_time_remaining)))
                progress_bar.set_postfix({"已处理": f"{processed_count}/{total_files}", "剩余时间": remaining_time})

    # 使用线程池处理文件
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = []
        for excel_file in excel_files:
            future = executor.submit(process_excel, excel_file, input_folder)
            future.add_done_callback(lambda f: update_progress(f.result()))
            futures.append(future)

        # 等待所有任务完成
        for future in futures:
            future.result()

    progress_bar.close()
    print(f"处理完成！总共处理了 {processed_count} 个文件")


if __name__ == "__main__":
    # 获取用户输入的文件夹路径
    folder_path = input("请输入包含 Excel 文件的文件夹路径: ")

    # 验证文件夹路径是否存在
    if not os.path.isdir(folder_path):
        print("输入的路径不是有效的文件夹！")
    else:
        # 开始处理
        process_folder(folder_path)
