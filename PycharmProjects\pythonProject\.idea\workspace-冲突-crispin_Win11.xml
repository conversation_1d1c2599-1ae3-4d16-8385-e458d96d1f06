<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="34b3e7d1-c3dc-4dff-9240-cbfcc71a56df" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Jupyter Notebook" />
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2qkVBQTNJj5ee1KRZcxJXQNlnhb" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Python.Digital_recognition.executor&quot;: &quot;Run&quot;,
    &quot;Python.Neural_network.executor&quot;: &quot;Run&quot;,
    &quot;Python.Test_agents.executor&quot;: &quot;Run&quot;,
    &quot;Python.backend_interagg.executor&quot;: &quot;Run&quot;,
    &quot;Python.context_data.executor&quot;: &quot;Run&quot;,
    &quot;Python.fenlei.executor&quot;: &quot;Run&quot;,
    &quot;Python.mnist.executor&quot;: &quot;Run&quot;,
    &quot;Python.post_ds.executor&quot;: &quot;Run&quot;,
    &quot;Python.sigmoid_function.executor&quot;: &quot;Run&quot;,
    &quot;Python.smol_agents.executor&quot;: &quot;Run&quot;,
    &quot;Python.testfunction.executor&quot;: &quot;Run&quot;,
    &quot;Python.tocsv.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/ProgramData/miniconda3/envs/smol_agents/python.exe&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;reference.settingsdialog.IDE.editor.colors.Console Colors&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="PyConsoleOptionsProvider">
    <option name="myPythonConsoleState">
      <console-settings module-name="pythonProject" is-module-sdk="true">
        <option name="myUseModuleSdk" value="true" />
        <option name="myModuleName" value="pythonProject" />
      </console-settings>
    </option>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-1d06a55b98c1-74d2a5396914-JavaScript-PY-241.14494.241" />
        <option value="bundled-python-sdk-0509580d9d50-28c9f5db9ffe-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-241.14494.241" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="34b3e7d1-c3dc-4dff-9240-cbfcc71a56df" name="Changes" comment="" />
      <created>1735206423490</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1735206423490</updated>
      <workItem from="1735206424448" duration="2062000" />
      <workItem from="1735266927467" duration="321000" />
      <workItem from="1735267358582" duration="52000" />
      <workItem from="1735267429279" duration="10669000" />
      <workItem from="1735436909141" duration="3690000" />
      <workItem from="1735548997181" duration="5647000" />
      <workItem from="1735614383988" duration="810000" />
      <workItem from="1735615280696" duration="11400000" />
      <workItem from="1736825622582" duration="3978000" />
      <workItem from="1741231313067" duration="13576000" />
      <workItem from="1741332506172" duration="3735000" />
      <workItem from="1741835771609" duration="2449000" />
      <workItem from="1741846473774" duration="3249000" />
      <workItem from="1742048914316" duration="2390000" />
      <workItem from="1742398131193" duration="1836000" />
      <workItem from="1742400029231" duration="4174000" />
      <workItem from="1742430316642" duration="1021000" />
      <workItem from="1742445797285" duration="2763000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/pythonProject$mnist.coverage" NAME="mnist Coverage Results" MODIFIED="1735615704584" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/深度学习/dataset" />
    <SUITE FILE_PATH="coverage/pythonProject$Digital_recognition.coverage" NAME="Digital_recognition Coverage Results" MODIFIED="1735631718320" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/深度学习" />
    <SUITE FILE_PATH="coverage/main_py$smol_agents.coverage" NAME="smol_agents Coverage Results" MODIFIED="1742402080778" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/pythonProject$Neural_network.coverage" NAME="Neural_network Coverage Results" MODIFIED="1735440510370" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/深度学习" />
    <SUITE FILE_PATH="coverage/main_py$tocsv.coverage" NAME="tocsv Coverage Results" MODIFIED="1741849250342" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/pythonProject$fenlei.coverage" NAME="fenlei Coverage Results" MODIFIED="1741322852724" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/pythonProject$testfunction.coverage" NAME="testfunction Coverage Results" MODIFIED="1735206505175" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/main_py$Test_agents.coverage" NAME="Test_agents Coverage Results" MODIFIED="1742430400726" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/main_py$context_data.coverage" NAME="context_data Coverage Results" MODIFIED="1741848723516" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/pythonProject$sigmoid_function.coverage" NAME="sigmoid_function Coverage Results" MODIFIED="1735267445328" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/深度学习" />
    <SUITE FILE_PATH="coverage/main_py$post_ds.coverage" NAME="post_ds Coverage Results" MODIFIED="1742398226942" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/pythonProject$backend_interagg.coverage" NAME="backend_interagg Coverage Results" MODIFIED="1735267160898" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="//wsl$/Ubuntu-22.04/root/.pycharm_helpers/pycharm_matplotlib_backend" />
  </component>
</project>