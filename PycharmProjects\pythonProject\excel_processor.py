import os
import warnings
import pandas as pd
import uuid
import random
import string

# 过滤openpyxl样式警告
warnings.filterwarnings('ignore', category=UserWarning, module='openpyxl.styles.stylesheet')

def process_excel_folder(input_folder):
    # 验证输入文件夹
    if not os.path.isdir(input_folder):
        print(f"错误：{input_folder} 不是有效目录")
        return
    
    # 创建输出文件夹
    parent_dir = os.path.dirname(input_folder)
    folder_name = os.path.basename(input_folder)
    output_root = os.path.join(parent_dir, f"{folder_name}output")
    os.makedirs(output_root, exist_ok=True)
    
    # 遍历输入文件夹中的Excel文件
    for file in os.listdir(input_folder):
        if file.endswith(('.xls', '.xlsx')):
            file_path = os.path.join(input_folder, file)
            try:
                # 读取Excel文件
                df = pd.read_excel(file_path)
                
                # 检查必要列是否存在
                required_columns = ['会话方A', '会话方B', '消息方向', '消息消息时间', '消息内容']
                if not all(col in df.columns for col in required_columns):
                    print(f"警告：文件 {file} 缺少必要列，跳过处理")
                    print(f"文件应包含以下列: {required_columns}")
                    print(f"实际列名: {df.columns.tolist()}")
                    continue
                
                # 按会话方A和B分组
                grouped = df.groupby(['会话方A', '会话方B'])
                
                # 创建二级输出文件夹
                file_count = len([f for f in os.listdir(input_folder) if f.endswith(('.xls', '.xlsx'))])
                output_subfolder = os.path.join(output_root, f"{os.path.splitext(file)[0]}_{file_count}")
                os.makedirs(output_subfolder, exist_ok=True)
                
                # 处理每个分组
                for (party_a, party_b), group in grouped:
                    # 生成会话ID (10位字母数字组合)
                    session_id = ''.join(random.choices(string.ascii_letters + string.digits, k=10))
                    
                    # 准备输出DataFrame
                    output_data = []
                    message_id = 1
                    
                    for _, row in group.iterrows():
                        # 确定发送方和接收方
                        if row['消息方向'] == '接收':
                            sender_id = row['会话方B']
                            receiver_id = row['会话方A']
                        else:  # 发送
                            sender_id = row['会话方A']
                            receiver_id = row['会话方B']
                        
                        # 构建输出行
                        output_row = {
                            '消息ID': message_id,
                            '发送方ID': sender_id,
                            '发送方所属机构': 'QT',
                            '发送方所属机构id': 'abc',
                            '发送方昵称': '琦玉',
                            '接收方ID': receiver_id,
                            '接收方所属机构': 'QQ',
                            '接收方所属机构id': 'cde',
                            '接收方昵称': '艾伦',
                            '会话ID': session_id,
                            '消息内容': row['消息内容'],
                            '解析场景': 1,
                            '消息时间': row['消息消息时间'],
                            '消息解析：0-单轮解析, 1-上下文解析': 1
                        }
                        output_data.append(output_row)
                        message_id += 1
                    
                    # 创建输出DataFrame
                    output_df = pd.DataFrame(output_data, columns=[
                        '消息ID', '发送方ID', '发送方所属机构', '发送方所属机构id', '发送方昵称',
                        '接收方ID', '接收方所属机构', '接收方所属机构id', '接收方昵称',
                        '会话ID', '消息内容', '解析场景', '消息时间', '消息解析：0-单轮解析, 1-上下文解析'
                    ])
                    
                    # 生成输出文件名
                    output_filename = f"{os.path.splitext(file)[0]}_{len(group)}.xlsx"
                    output_path = os.path.join(output_subfolder, output_filename)
                    
                    # 写入Excel文件
                    output_df.to_excel(output_path, index=False)
                    print(f"已生成文件: {output_path}")
                
            except Exception as e:
                print(f"处理文件 {file} 时出错: {str(e)}")

if __name__ == "__main__":
    import sys
    if len(sys.argv) != 2:
        input_folder = input("请输入Excel文件所在文件夹路径: ")
        process_excel_folder(input_folder)
    else:
        process_excel_folder(sys.argv[1])
