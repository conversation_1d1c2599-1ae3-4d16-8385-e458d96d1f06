{"cells": [{"cell_type": "code", "id": "initial_id", "metadata": {"collapsed": true, "ExecuteTime": {"end_time": "2025-01-14T03:49:05.965730Z", "start_time": "2025-01-14T03:47:35.501467Z"}}, "source": ["import numpy as np\n", "import tensorflow as tf\n", "from tensorflow.keras import layers, models\n", "from tensorflow.keras.datasets import mnist\n", "import matplotlib.pyplot as plt\n", "\n", "# 加载MNIST数据集\n", "(train_images, train_labels), (test_images, test_labels) = mnist.load_data()\n", "\n", "# 数据预处理\n", "train_images = train_images.reshape((60000, 28, 28, 1)).astype('float32') / 255\n", "test_images = test_images.reshape((10000, 28, 28, 1)).astype('float32') / 255\n", "\n", "# 构建CNN模型\n", "model = models.Sequential([\n", "    layers.Conv2D(32, (3, 3), activation='relu', input_shape=(28, 28, 1)),\n", "    layers.MaxPooling2D((2, 2)),\n", "    layers.Conv2D(64, (3, 3), activation='relu'),\n", "    layers.MaxPooling2D((2, 2)),\n", "    layers.Conv2D(64, (3, 3), activation='relu'),\n", "    layers.<PERSON><PERSON>(),\n", "    layers.Dense(64, activation='relu'),\n", "    layers.Dense(10, activation='softmax')\n", "])\n", "\n", "# 编译模型\n", "model.compile(optimizer='adam',\n", "              loss='sparse_categorical_crossentropy',\n", "              metrics=['accuracy'])\n", "\n", "# 训练模型\n", "history = model.fit(train_images, train_labels, epochs=5, \n", "                    validation_data=(test_images, test_labels))\n", "\n", "# 评估模型\n", "test_loss, test_acc = model.evaluate(test_images, test_labels)\n", "print(f'\\n测试准确率: {test_acc:.3f}')\n", "\n", "# 可视化训练结果\n", "plt.figure(figsize=(12, 4))\n", "\n", "plt.subplot(1, 2, 1)\n", "plt.plot(history.history['accuracy'], label='训练准确率')\n", "plt.plot(history.history['val_accuracy'], label='验证准确率')\n", "plt.xlabel('轮次')\n", "plt.ylabel('准确率')\n", "plt.legend()\n", "\n", "plt.subplot(1, 2, 2)\n", "plt.plot(history.history['loss'], label='训练损失')\n", "plt.plot(history.history['val_loss'], label='验证损失')\n", "plt.xlabel('轮次')\n", "plt.ylabel('损失')\n", "plt.legend()\n", "plt.show()\n", "\n", "# 进行预测\n", "predictions = model.predict(test_images[:5])\n", "print(\"\\n前5个测试样本的预测结果:\")\n", "for i in range(5):\n", "    print(f\"真实值: {test_labels[i]}, 预测值: {np.argmax(predictions[i])}\")\n", "\n"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Downloading data from https://storage.googleapis.com/tensorflow/tf-keras-datasets/mnist.npz\n", "\u001b[1m11490434/11490434\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m4s\u001b[0m 0us/step\n", "Epoch 1/5\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\.conda\\envs\\Aurora\\lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m1875/1875\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m17s\u001b[0m 8ms/step - accuracy: 0.8922 - loss: 0.3415 - val_accuracy: 0.9826 - val_loss: 0.0578\n", "Epoch 2/5\n", "\u001b[1m1875/1875\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m15s\u001b[0m 8ms/step - accuracy: 0.9849 - loss: 0.0469 - val_accuracy: 0.9892 - val_loss: 0.0353\n", "Epoch 3/5\n", "\u001b[1m1875/1875\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m14s\u001b[0m 8ms/step - accuracy: 0.9893 - loss: 0.0326 - val_accuracy: 0.9887 - val_loss: 0.0389\n", "Epoch 4/5\n", "\u001b[1m1875/1875\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m15s\u001b[0m 8ms/step - accuracy: 0.9919 - loss: 0.0260 - val_accuracy: 0.9916 - val_loss: 0.0312\n", "Epoch 5/5\n", "\u001b[1m1875/1875\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m16s\u001b[0m 9ms/step - accuracy: 0.9934 - loss: 0.0213 - val_accuracy: 0.9909 - val_loss: 0.0307\n", "\u001b[1m313/313\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m2s\u001b[0m 5ms/step - accuracy: 0.9886 - loss: 0.0387\n", "\n", "测试准确率: 0.991\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\.conda\\envs\\Aurora\\lib\\site-packages\\IPython\\core\\pylabtools.py:152: UserWarning: Glyph 36718 (\\N{CJK UNIFIED IDEOGRAPH-8F6E}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\.conda\\envs\\Aurora\\lib\\site-packages\\IPython\\core\\pylabtools.py:152: UserWarning: Glyph 27425 (\\N{CJK UNIFIED IDEOGRAPH-6B21}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\.conda\\envs\\Aurora\\lib\\site-packages\\IPython\\core\\pylabtools.py:152: UserWarning: Glyph 20934 (\\N{CJK UNIFIED IDEOGRAPH-51C6}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\.conda\\envs\\Aurora\\lib\\site-packages\\IPython\\core\\pylabtools.py:152: UserWarning: Glyph 30830 (\\N{CJK UNIFIED IDEOGRAPH-786E}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\.conda\\envs\\Aurora\\lib\\site-packages\\IPython\\core\\pylabtools.py:152: UserWarning: Glyph 29575 (\\N{CJK UNIFIED IDEOGRAPH-7387}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\.conda\\envs\\Aurora\\lib\\site-packages\\IPython\\core\\pylabtools.py:152: UserWarning: Glyph 35757 (\\N{CJK UNIFIED IDEOGRAPH-8BAD}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\.conda\\envs\\Aurora\\lib\\site-packages\\IPython\\core\\pylabtools.py:152: UserWarning: Glyph 32451 (\\N{CJK UNIFIED IDEOGRAPH-7EC3}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\.conda\\envs\\Aurora\\lib\\site-packages\\IPython\\core\\pylabtools.py:152: UserWarning: Glyph 39564 (\\N{CJK UNIFIED IDEOGRAPH-9A8C}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\.conda\\envs\\Aurora\\lib\\site-packages\\IPython\\core\\pylabtools.py:152: UserWarning: Glyph 35777 (\\N{CJK UNIFIED IDEOGRAPH-8BC1}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\.conda\\envs\\Aurora\\lib\\site-packages\\IPython\\core\\pylabtools.py:152: UserWarning: Glyph 25439 (\\N{CJK UNIFIED IDEOGRAPH-635F}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\.conda\\envs\\Aurora\\lib\\site-packages\\IPython\\core\\pylabtools.py:152: UserWarning: Glyph 22833 (\\N{CJK UNIFIED IDEOGRAPH-5931}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n"]}, {"data": {"text/plain": ["<Figure size 1200x400 with 2 Axes>"], "image/png": "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"}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 164ms/step\n", "\n", "前5个测试样本的预测结果:\n", "真实值: 7, 预测值: 7\n", "真实值: 2, 预测值: 2\n", "真实值: 1, 预测值: 1\n", "真实值: 0, 预测值: 0\n", "真实值: 4, 预测值: 4\n"]}], "execution_count": 3}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "fd09ae9212bf0cf"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}