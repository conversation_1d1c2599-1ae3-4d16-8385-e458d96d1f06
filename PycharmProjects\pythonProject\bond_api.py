from flask import Flask, request, jsonify

app = Flask(__name__)

# 禁用Flask默认的访问日志
import logging
log = logging.getLogger('werkzeug')
log.disabled = True

@app.route('/api/order', methods=['POST'])
def process_order():
    # 获取订单数据
    order_data = request.get_json()
    
    # 检查必填字段
    required_fields = ['direction', 'bond_name', 'price', 'volume', 'delivery']
    missing_fields = [field for field in required_fields if not order_data.get(field)]
    
    if missing_fields:
        client_ip = request.remote_addr
        print(f"\n错误: 订单信息不完整 (来自IP: {client_ip})")
        print(f"缺少字段: {', '.join(missing_fields)}")
        print(f"请求数据: {order_data}")
        return jsonify({
            "error": "订单信息不完整",
            "missing_fields": missing_fields
        }), 400
    
    # 打印新订单信息
    client_ip = request.remote_addr
    print(f"\n新订单 (来自IP: {client_ip})")
    
    # 打印订单详情
    print("\n订单详情:")
    print(f"方向: {order_data['direction']}")
    print(f"债券名称: {order_data['bond_name']}")
    print(f"价格: {order_data['price']}")
    print(f"数量: {order_data['volume']}")
    print(f"交割日: {order_data['delivery']}")
    
    # 终端交互确认
    while True:
        confirm = input("\n请确认订单 (yes/no): ").strip().lower()
        if confirm in ['yes', 'no']:
            break
        print("无效输入，请输入yes或no")
    
    # 返回结果
    if confirm == 'yes':
        response = "done\n" + " ".join([
            order_data['bond_name'],
            order_data['price'],
            order_data['volume'],
            order_data['delivery']
        ])
        return response, 200
    else:
        return "抱歉，没有合适的报价！", 200

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5001, debug=True, use_reloader=False)
