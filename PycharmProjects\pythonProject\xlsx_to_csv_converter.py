import os
import pandas as pd

def convert_xlsx_to_csv(folder_path):
    """
    将指定文件夹及其子文件夹中的所有.xlsx文件转换为.csv文件
    :param folder_path: 包含.xlsx文件的文件夹路径
    """
    for root, dirs, files in os.walk(folder_path):
        for file in files:
            if file.endswith('.xlsx'):
                xlsx_path = os.path.join(root, file)
                csv_path = os.path.splitext(xlsx_path)[0] + '.csv'
                
                try:
                    # 读取Excel文件
                    df = pd.read_excel(xlsx_path)
                    # 写入CSV文件，覆盖已存在的文件
                    df.to_csv(csv_path, index=False)
                    print(f"转换成功: {xlsx_path} -> {csv_path}")
                except Exception as e:
                    print(f"转换失败: {xlsx_path} - 错误: {str(e)}")

if __name__ == "__main__":
    folder_path = input("请输入包含.xlsx文件的文件夹路径: ")
    convert_xlsx_to_csv(folder_path)
