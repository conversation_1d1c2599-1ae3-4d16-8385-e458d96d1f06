from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_core.models import ModelInfo

model_client=OpenAIChatCompletionClient(
            model="qwen/qwq-32b:free",
            base_url="https://openrouter.ai/api/v1",
            api_key="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",
            model_info=ModelInfo(
                vision=False,
                function_calling=True,
                json_output=True,
                family="qwq",
            )
        )
print(model_client.dump_component().model_dump_json())
