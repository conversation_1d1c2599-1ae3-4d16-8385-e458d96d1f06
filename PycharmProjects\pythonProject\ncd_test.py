import re

def is_unwanted_content(line):
    """判断文本行是否包含不想要的内容。

    如果文本行长度小于 3，或者包含特定的广告/促销词语，
    或者长度在 3 到 8 之间且包含 "CD"（不区分大小写），
    或者处理后以数字开头后跟字母，或者包含特定的交易/标题相关词语，
    则返回 True，否则返回 False。

    :param str line: 语料
    :return: True 如果包含不想要的内容，False 否则
    :rtype: bool
    """
    llen = len(line)
    lower_line = line.lower()
    cleaned_line = re.sub(r"广发|发行|派发|发展|发电|发光|发货|发动|发生|，|、|出现|出租|出自|出差|出力|出入|出逃|出血|出境|出国", "", lower_line)

    if llen < 3:
        return True
    if re.search(r"(押庄|广告|招商|馈赠)", line):
        return True
    if 3 <= llen <= 8 and "cd" in lower_line:
        return True
    if re.search(r"^\d+[a-zA-Z]+", cleaned_line):
        return True
    if re.search(r"(买|卖|sell|buy|bid|ofr|盘|标题|头寸)", cleaned_line):
        return True

    return False


line = input("输入要解析的文本：")
print(is_unwanted_content(line))