# 深度学习入门

关注微信公众号：`正版乔`

如果你觉得不错，可以提供小额打赏，谢谢支持。[面包🍞 面包多 / 顿顿饭](https://dun.mianbaoduo.com/@qiao)

## 文件结构

|文件夹名   |说明                         |
|:--        |:--                          |
|ch01       |第1章使用的源代码            |
|ch02       |第2章使用的源代码            |
|...        |...                          |
|ch08       |第8章使用的源代码            |
|common     |共同使用的源代码             |
|dataset    |数据集用的源代码             |


源代码的解释请参考本书。

## 必要条件
执行源代码需要按照以下软件。

* Python 3.x
* NumPy
* Matplotlib

※Python的版本为Python 3。

## 执行方法

前进到各章节的文件夹，执行Python命令。

```
$ cd ch01
$ python man.py

$ cd ../ch05
$ python train_nueralnet.py
```

## 使用许可

本源代码使用[MIT许可协议](http://www.opensource.org/licenses/MIT)。
无论是否为商业行为，均可自由使用。

## 勘误表

本书的勘误信息在以下网址中公开。读者可以在以下网址中查看和提交勘误。

http://www.ituring.com.cn/book/1921


