import akshare as ak
from flask import Flask, request, jsonify
import pandas as pd

app = Flask(__name__)
app.config['JSON_AS_ASCII'] = False # 配置 Flask 以正确显示中文字符

def get_bond_deal(bond_name):
    """
    使用 akshare 获取债券现券成交数据。

    参数:
        bond_name (str): 要搜索的债券名称。

    返回:
        pandas.DataFrame: 包含债券现券成交数据的 DataFrame，
                          如果发生错误或未找到数据，则返回 None。
    """
    payload = {
        "flag": "1",
        "lang": "cn",
        "bondName": bond_name,
    }
    try:
        bond_spot_deal_df = ak.bond_spot_deal(payload)
        # 检查 DataFrame 是否为空或包含特定的错误消息（如果适用）
        if bond_spot_deal_df is None or bond_spot_deal_df.empty:
             print(f"未找到债券数据: {bond_name}")
             return None
        print(bond_spot_deal_df)
        return bond_spot_deal_df
    except Exception as e:
        print(f"获取 {bond_name} 数据时出错: {e}")
        return None

@app.route('/bond_deal', methods=['POST'])
def bond_deal_api():
    """
    获取债券现券成交数据的 API 端点。
    需要一个包含 'bondName' 键的 JSON 负载。
    """
    print("--- New Request ---") # 添加日志分隔符
    print("Request Headers:")
    print(request.headers)
    print("Request Raw Data:")
    print(request.data) # 打印原始请求体数据
    print("Is JSON:", request.is_json) # 检查 Flask 是否识别为 JSON

    if not request.is_json:
        print("Error: Request Content-Type is not application/json or body is invalid JSON.") # 添加错误日志
        return jsonify({"error": "请求必须是 JSON 格式"}), 400

    try:
        data = request.get_json()
        print("Parsed JSON data:", data) # 打印解析后的 JSON
    except Exception as e:
        print(f"Error parsing JSON: {e}") # 添加 JSON 解析错误日志
        return jsonify({"error": "无法解析请求体中的 JSON 数据"}), 400

    if data is None: # 检查 get_json() 是否返回 None
        print("Error: get_json() returned None.")
        return jsonify({"error": "无法解析请求体中的 JSON 数据"}), 400

    bond_name = data.get('bondName')
    print("Extracted bondName:", bond_name) # 打印提取的 bondName

    if not bond_name:
        print("Error: 'bondName' key missing in JSON payload.") # 添加键缺失错误日志
        return jsonify({"error": "请求负载中缺少 'bondName'"}), 400

    result_df = get_bond_deal(bond_name)

    if result_df is None:
        # 如果没有数据或发生错误，则返回空列表或适当的错误消息
        return jsonify({"error": f"无法检索到债券数据: {bond_name}"}), 404
        # 或者，如果没有数据，则返回 jsonify([])

    # 将 DataFrame 转换为适合 API 响应的 JSON 格式
    # 'records' 格式: [{"列1": 值1, "列2": 值2}, ...]
    result_json = result_df.to_json(orient="records", force_ascii=False)
    # 注意: to_json 返回一个 JSON 字符串, Flask jsonify 需要一个 dict/list 来序列化
    import json
    return jsonify(json.loads(result_json))


if __name__ == '__main__':
    # 运行 Flask 开发服务器
    # 可通过本地计算机访问 http://127.0.0.1:5000/
    app.run(host='0.0.0.0', debug=True) # debug=True 用于开发环境, 生产环境设置为 False
