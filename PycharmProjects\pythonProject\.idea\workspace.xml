<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="34b3e7d1-c3dc-4dff-9240-cbfcc71a56df" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Jupyter Notebook" />
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="FlaskConsoleOptions" custom-start-script="import sys&#10;sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;from flask.cli import ScriptInfo&#10;locals().update(ScriptInfo(create_app=None).load_app().make_shell_context())&#10;print(&quot;Python %s on %s\nApp: %s [%s]\nInstance: %s&quot; % (sys.version, sys.platform, app.import_name, app.env, app.instance_path))">
    <envs>
      <env key="FLASK_APP" value="app" />
    </envs>
    <option name="myCustomStartScript" value="import sys&#10;sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;from flask.cli import ScriptInfo&#10;locals().update(ScriptInfo(create_app=None).load_app().make_shell_context())&#10;print(&quot;Python %s on %s\nApp: %s [%s]\nInstance: %s&quot; % (sys.version, sys.platform, app.import_name, app.env, app.instance_path))" />
    <option name="myEnvs">
      <map>
        <entry key="FLASK_APP" value="app" />
      </map>
    </option>
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://D:/ProgramData/miniconda3/envs/smol_agents/Lib/site-packages/smolagents/models.py" root0="SKIP_INSPECTION" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2qkVBQTNJj5ee1KRZcxJXQNlnhb" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Python.Digital_recognition.executor&quot;: &quot;Run&quot;,
    &quot;Python.Neural_network.executor&quot;: &quot;Run&quot;,
    &quot;Python.Test_agents.executor&quot;: &quot;Run&quot;,
    &quot;Python.akshare.executor&quot;: &quot;Run&quot;,
    &quot;Python.autogen_sdk.executor&quot;: &quot;Run&quot;,
    &quot;Python.backend_interagg.executor&quot;: &quot;Run&quot;,
    &quot;Python.batch_api_call.executor&quot;: &quot;Run&quot;,
    &quot;Python.bond_china.executor&quot;: &quot;Run&quot;,
    &quot;Python.bond_search.executor&quot;: &quot;Run&quot;,
    &quot;Python.context_data.executor&quot;: &quot;Run&quot;,
    &quot;Python.fenlei.executor&quot;: &quot;Run&quot;,
    &quot;Python.get_jwt.executor&quot;: &quot;Run&quot;,
    &quot;Python.json_to_excel.executor&quot;: &quot;Run&quot;,
    &quot;Python.mnist.executor&quot;: &quot;Run&quot;,
    &quot;Python.ncd_test.executor&quot;: &quot;Run&quot;,
    &quot;Python.post_ds.executor&quot;: &quot;Run&quot;,
    &quot;Python.sigmoid_function.executor&quot;: &quot;Run&quot;,
    &quot;Python.smol_agents.executor&quot;: &quot;Run&quot;,
    &quot;Python.test-openrouter.executor&quot;: &quot;Run&quot;,
    &quot;Python.test-to-speech-qwen-tts.executor&quot;: &quot;Run&quot;,
    &quot;Python.test_remote(1).executor&quot;: &quot;Run&quot;,
    &quot;Python.testfunction.executor&quot;: &quot;Run&quot;,
    &quot;Python.tocsv.executor&quot;: &quot;Debug&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/Documents/augment-projects/幼小衔接app项目&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;settings.sync&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="PyConsoleOptionsProvider">
    <option name="myPythonConsoleState">
      <console-settings module-name="pythonProject" is-module-sdk="true">
        <option name="myUseModuleSdk" value="true" />
        <option name="myModuleName" value="pythonProject" />
      </console-settings>
    </option>
  </component>
  <component name="RunManager">
    <configuration name="bond_china" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="pythonProject" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$USER_HOME$/.conda/envs/smolagents/Lib/site-packages/akshare/bond" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$USER_HOME$/.conda/envs/smolagents/Lib/site-packages/akshare/bond/bond_china.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="bond_search" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="pythonProject" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/bond_search.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="smol_agents" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="pythonProject" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/smol_agents.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="tocsv" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="pythonProject" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/tocsv.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.bond_china" />
        <item itemvalue="Python.tocsv" />
        <item itemvalue="Python.smol_agents" />
        <item itemvalue="Python.bond_search" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-1d06a55b98c1-74d2a5396914-JavaScript-PY-241.14494.241" />
        <option value="bundled-python-sdk-0509580d9d50-28c9f5db9ffe-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-241.14494.241" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="34b3e7d1-c3dc-4dff-9240-cbfcc71a56df" name="Changes" comment="" />
      <created>1735206423490</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1735206423490</updated>
      <workItem from="1735206424448" duration="2062000" />
      <workItem from="1735266927467" duration="321000" />
      <workItem from="1735267358582" duration="52000" />
      <workItem from="1735267429279" duration="10669000" />
      <workItem from="1735436909141" duration="3690000" />
      <workItem from="1735548997181" duration="5647000" />
      <workItem from="1735614383988" duration="810000" />
      <workItem from="1735615280696" duration="11400000" />
      <workItem from="1736825622582" duration="3978000" />
      <workItem from="1741231313067" duration="13576000" />
      <workItem from="1741332506172" duration="3735000" />
      <workItem from="1741835771609" duration="2449000" />
      <workItem from="1741846473774" duration="3249000" />
      <workItem from="1742048914316" duration="2390000" />
      <workItem from="1742398131193" duration="1836000" />
      <workItem from="1742400029231" duration="4174000" />
      <workItem from="1742430316642" duration="1021000" />
      <workItem from="1742445797285" duration="5755000" />
      <workItem from="1742490102869" duration="256000" />
      <workItem from="1742490369135" duration="6504000" />
      <workItem from="1742516650118" duration="9895000" />
      <workItem from="1742661405693" duration="12854000" />
      <workItem from="1742747403581" duration="1498000" />
      <workItem from="1742839016128" duration="4296000" />
      <workItem from="1742991177199" duration="20000" />
      <workItem from="1743061708111" duration="36971000" />
      <workItem from="1744025159383" duration="3197000" />
      <workItem from="1744094496518" duration="1560000" />
      <workItem from="1744871412454" duration="34000" />
      <workItem from="1745910066437" duration="2626000" />
      <workItem from="1746207172048" duration="251000" />
      <workItem from="1747033723553" duration="370000" />
      <workItem from="1747040671504" duration="1404000" />
      <workItem from="1747120746906" duration="2115000" />
      <workItem from="1747384157156" duration="97000" />
      <workItem from="1747384497470" duration="6893000" />
      <workItem from="1748933296224" duration="2828000" />
      <workItem from="1749720186781" duration="3377000" />
      <workItem from="1751350853791" duration="7090000" />
      <workItem from="1751436187058" duration="4000000" />
      <workItem from="1752048488456" duration="529000" />
      <workItem from="1753255220357" duration="888000" />
      <workItem from="1753757307297" duration="1184000" />
      <workItem from="1753857339760" duration="1285000" />
      <workItem from="1753959453844" duration="1692000" />
      <workItem from="1754287083665" duration="2655000" />
      <workItem from="1754448294388" duration="2572000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$USER_HOME$/Documents/augment-projects/幼小衔接app项目/batch_api_call.py</url>
          <line>65</line>
          <option name="timeStamp" value="2" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$USER_HOME$/Documents/augment-projects/幼小衔接app项目/batch_api_call.py</url>
          <line>73</line>
          <option name="timeStamp" value="4" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$USER_HOME$/Documents/augment-projects/幼小衔接app项目/batch_api_call.py</url>
          <line>78</line>
          <option name="timeStamp" value="5" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/main_py$test_openrouter.coverage" NAME="test-openrouter 覆盖结果" MODIFIED="1742706464188" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/main_py$post_ds.coverage" NAME="post_ds Coverage Results" MODIFIED="1742398226942" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/main_py$ncd_test.coverage" NAME="ncd_test 覆盖结果" MODIFIED="1747034080125" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/pythonProject$sigmoid_function.coverage" NAME="sigmoid_function Coverage Results" MODIFIED="1735267445328" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/深度学习" />
    <SUITE FILE_PATH="coverage/main_py$autogen_sdk.coverage" NAME="autogen_sdk 覆盖结果" MODIFIED="1742746578222" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/pythonProject$mnist.coverage" NAME="mnist Coverage Results" MODIFIED="1735615704584" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/深度学习/dataset" />
    <SUITE FILE_PATH="coverage/main_py$smol_agents.coverage" NAME="smol_agents Coverage Results" MODIFIED="1744025213811" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/pythonProject$backend_interagg.coverage" NAME="backend_interagg Coverage Results" MODIFIED="1735267160898" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="//wsl$/Ubuntu-22.04/root/.pycharm_helpers/pycharm_matplotlib_backend" />
    <SUITE FILE_PATH="coverage/main_py$batch_api_call.coverage" NAME="batch_api_call 覆盖结果" MODIFIED="1745912229365" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$USER_HOME$/Documents/augment-projects/幼小衔接app项目" />
    <SUITE FILE_PATH="coverage/main_py$bond_search.coverage" NAME="bond_search Coverage Results" MODIFIED="1743668104630" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/main_py$fenlei.coverage" NAME="fenlei 覆盖结果" MODIFIED="1753857375301" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/pythonProject$Neural_network.coverage" NAME="Neural_network Coverage Results" MODIFIED="1735440510370" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/深度学习" />
    <SUITE FILE_PATH="coverage/pythonProject$testfunction.coverage" NAME="testfunction Coverage Results" MODIFIED="1735206505175" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/main_py$get_jwt.coverage" NAME="get_jwt 覆盖结果" MODIFIED="1747122749841" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/main_py$bond_china.coverage" NAME="bond_china 覆盖结果" MODIFIED="1754448851946" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$USER_HOME$/.conda/envs/smolagents/Lib/site-packages/akshare/bond" />
    <SUITE FILE_PATH="coverage/main_py$Test_agents.coverage" NAME="Test_agents 覆盖结果" MODIFIED="1742665319364" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/pythonProject$fenlei.coverage" NAME="fenlei Coverage Results" MODIFIED="1741322852724" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/main_py$test_remote_1_.coverage" NAME="test_remote(1) 覆盖结果" MODIFIED="1747647127004" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$USER_HOME$/Documents/WXWork/1688851256476427/Cache/File/2025-05" />
    <SUITE FILE_PATH="coverage/main_py$test_to_speech_qwen_tts.coverage" NAME="test-to-speech-qwen-tts 覆盖结果" MODIFIED="1751439368833" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/pythonProject$Digital_recognition.coverage" NAME="Digital_recognition Coverage Results" MODIFIED="1735631718320" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/深度学习" />
    <SUITE FILE_PATH="coverage/main_py$context_data.coverage" NAME="context_data Coverage Results" MODIFIED="1741848723516" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/main_py$json_to_excel.coverage" NAME="json_to_excel 覆盖结果" MODIFIED="1754537246417" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/main_py$akshare.coverage" NAME="akshare Coverage Results" MODIFIED="1743062722365" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/main_py$tocsv.coverage" NAME="tocsv 覆盖结果" MODIFIED="1745910136718" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
  </component>
</project>