import pandas as pd
from openpyxl import load_workbook
from openpyxl.styles import PatternFill

def compare_excel_by_row(file1, file2):
    # 加载第一个和第二个Excel文件
    xls1 = pd.ExcelFile(file1)
    xls2 = pd.ExcelFile(file2)


    # 生成输出文件路径，与file2相同位置
    output_file = file2.replace(".xlsx", "_compared.xlsx")

    # 创建一个输出文件的写入器
    with pd.ExcelWriter(output_file, engine="openpyxl") as writer:
        # 遍历第二个文件的所有Sheet页
        for sheet_name in xls2.sheet_names:
            df2 = xls2.parse(sheet_name)

            # 如果第一个文件包含相同名称的Sheet页
            if sheet_name in xls1.sheet_names:
                df1 = xls1.parse(sheet_name)

                # 确保df1和df2的形状相同才能比较
                if df1.shape == df2.shape:
                    # 生成差异掩码
                    diff_mask = (df1 != df2)

                    # 将第二个文件的内容写入输出文件
                    df2.to_excel(writer, sheet_name=sheet_name, index=False)

                    # 获取当前写入的Sheet页，用于高亮差异
                    workbook = writer.book
                    worksheet = workbook[sheet_name]

                    # 高亮填充样式
                    fill = PatternFill(start_color="FFFF00", end_color="FFFF00", fill_type="solid")

                    # 遍历每一行每一列的单元格，将不同单元格高亮
                    for row in range(len(df2)):
                        for col in range(len(df2.columns)):
                            if diff_mask.iat[row, col]:  # 如果单元格不同
                                worksheet.cell(row=row + 2, column=col + 1).fill = fill
                else:
                    print(f"Sheet '{sheet_name}'在两个文件中形状不同，跳过该Sheet。")
            else:
                # 如果第一个文件没有此Sheet页，则直接写入第二个文件的内容
                df2.to_excel(writer, sheet_name=sheet_name, index=False)

    print(f"文件比较完成，输出结果已保存到 '{output_file}'")

# 使用示例
file1 = r"/root/project/excel1.xlsx"  # 第一个Excel文件路径
file2 = r"/root/project/excel2.xlsx"  # 第二个Excel文件路径

compare_excel_by_row(file1, file2)