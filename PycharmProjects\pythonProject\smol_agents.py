from smolagents import LiteLLMModel,ToolCollection, CodeAgent
from mcp import StdioServerParameters
import os

# 使用ollama api
model = LiteLLMModel(
    model_id="openrouter/qwen/qwq-32b:free",
    api_base="https://openrouter.ai/api/v1/chat/completions",
    api_key = "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"
)

    # 调用本地模型
    # model_id="ollama/deepseek-r1:1.5b",
    # api_base="http://127.0.0.1:11434",
    # api_key="ollama",


server_parameters = StdioServerParameters(
    command="python",
    args=["C:\\Users\<USER>\mcp-server-demo\\file.py"],
)



# agent = CodeAgent(tools=[], model=model)
#
# # 测试智能体
# agent.run("smolagents如何使用mcp")

with ToolCollection.from_mcp(server_parameters) as tool_collection:
    agent = CodeAgent(tools=[*tool_collection.tools], model=model, add_base_tools=True)
    result = agent.run("桌面有多少个文件？")
