

import os
import google.generativeai as genai
from google.api_core.exceptions import GoogleAPICallError
import argparse

# --- 配置你的API密钥 ---
# 强烈建议使用环境变量来保护你的API密钥。
# 在你的命令行中设置:
# set GOOGLE_API_KEY=你的API密钥 (Windows)
# export GOOGLE_API_KEY='你的API密钥' (Linux/macOS)
API_KEY = os.getenv("GOOGLE_API_KEY")

def configure_api():
    """配置Gemini API"""
    if not API_KEY:
        raise ValueError("错误：GOOGLE_API_KEY环境变量未设置。请先设置你的API密钥。")
    genai.configure(api_key=API_KEY)

def get_hot_topics():
    """
    模拟获取小红书热门话题。
    在实际应用中，这可以通过爬虫或调用专门的API实现。
    这里我们用固定的例子代替。
    """
    return [
        "夏日防晒攻略",
        "周末探店",
        "OOTD (今日穿搭)",
        "我的私藏书单",
        "高效学习法"
    ]

def choose_topic():
    """让用户选择或输入话题"""
    print("--- 小红书热门话题 ---")
    topics = get_hot_topics()
    for i, topic in enumerate(topics):
        print(f"{i + 1}. {topic}")
    print("--------------------")
    
    choice = input("请选择一个话题序号，或直接输入你自己的话题: ")
    
    try:
        topic_index = int(choice) - 1
        if 0 <= topic_index < len(topics):
            return topics[topic_index]
        else:
            print("无效序号，将作为自定义话题处理。")
            return choice
    except ValueError:
        # 用户输入的是字符串，作为自定义话题
        return choice

def search_for_info(topic):
    """
    使用Google搜索获取关于话题的背景信息。
    注意：这是一个模拟函数，实际项目中你需要集成一个搜索API。
    这里我们返回一个固定的字符串来模拟搜索结果。
    """
    print(f"\n正在搜索关于“{topic}”的最新信息...")
    # 在真实场景中，这里会调用 google_web_search 工具
    # return default_api.google_web_search(query=f"最新 {topic} 技巧和产品")
    
    # 为了本地可运行，我们返回一个模拟的搜索结果
    mock_search_results = {
        "夏日防晒攻略": "近期热门防晒产品包括安热沙金瓶、EltaMD等。物理防晒和化学防晒相结合是关键。推荐使用SPF50+ PA++++的产品，并注意每2小时补涂一次。",
        "周末探店": "上海新开的网红咖啡店'Coffee Corner'备受好评，主打手冲和特调咖啡。北京的'Hutong Vibe'则以其独特的复古装修和创意甜品吸引了大量顾客。",
        "OOTD (今日穿搭)": "今年夏天流行多巴胺穿搭，以鲜艳明亮的色彩为主。例如，亮黄色连衣裙搭配草编包。Cleanfit风格也很受欢迎，主打简约、干净的配色。",
        "我的私藏书单": "近期畅销书包括刘慈欣的《三体》系列，以及心理学书籍《被讨厌的勇气》。很多人推荐在睡前阅读半小时，有助于放松和提升自我。",
        "高效学习法": "费曼学习法和番茄工作法是目前最受推崇的两种方法。许多学生使用Notion或Obsidian等工具来做笔记和管理知识体系。"
    }
    return mock_search_results.get(topic, f"关于“{topic}”的搜索结果：这是一个需要用户自行填充的自定义话题信息。建议查找最新的产品、技巧或地点。")


def generate_article(topic, search_results):
    """使用Gemini API生成小红书文章"""
    print("正在连接Gemini API生成文章...")
    
    # 创建一个精心设计的Prompt
    prompt = f"""
    **角色扮演**: 你是一位资深的小红书内容创作者。

    **任务**: 请根据我提供的主题和背景信息，创作一篇地道的小redbook风格的图文笔记。

    **主题**: {topic}

    **背景信息**: 
    {search_results}

    **写作要求**:
    1.  **标题**: 必须吸引人，使用emoji开头，字数在15字以内。
    2.  **正文**:
        -   总字数在200-400字之间。
        -   开头要引人入胜，能迅速抓住读者注意力。
        -   内容要分段，段落之间空一行，保持简洁易读。
        -   多使用emoji来增加趣味性和视觉效果，每个段落至少有1-2个emoji。
        -   语言风格要口语化、亲切，就像和朋友聊天一样。
        -   在适当的地方自然地融入背景信息里的关键词或产品。
    3.  **结尾**:
        -   在文章末尾，必须加上相关的**Hashtags** (标签)，至少5个。
        -   标签要紧扣主题，例如`#小红书` `#笔记灵感` `#{topic}`等。

    请严格按照以上要求开始你的创作。
    """

    try:
        model = genai.GenerativeModel('gemini-pro')
        response = model.generate_content(prompt)
        return response.text
    except GoogleAPICallError as e:
        return f"API调用失败: {e}"
    except Exception as e:
        return f"发生未知错误: {e}"

def main():
    """主函数"""
    try:
        configure_api()
        
        # 1. 获取话题
        topic = choose_topic()
        
        # 2. 搜集信息
        info = search_for_info(topic)
        
        # 3. 生成文章
        article = generate_article(topic, info)
        
        # 4. 输出结果
        print("\n--- ✨ 生成的小红书文章 ✨ ---\n")
        print(article)
        print("\n---------------------------------")

    except ValueError as e:
        print(e)
    except Exception as e:
        print(f"程序运行出错: {e}")

if __name__ == "__main__":
    main()

